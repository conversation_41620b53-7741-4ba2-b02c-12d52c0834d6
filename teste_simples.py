#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste simples para o arquivo Teste.py
Este teste executa o script e verifica se funciona corretamente
"""

import unittest
import subprocess
import sys
import os
from unittest.mock import patch, Mock

class TestTesteSimples(unittest.TestCase):
    """Testes simples para o arquivo Teste.py"""
    
    def test_arquivo_existe(self):
        """Verifica se o arquivo Teste.py existe"""
        self.assertTrue(os.path.exists('Teste.py'), "Arquivo Teste.py deve existir")
    
    def test_execucao_sem_erro_sintaxe(self):
        """Testa se o script executa sem erros de sintaxe"""
        try:
            # Executa o script e captura a saída
            result = subprocess.run([
                sys.executable, '-c', 
                '''
try:
    exec(open("Teste.py").read())
except Exception as e:
    print(f"ERRO_EXECUCAO: {e}")
                '''
            ], capture_output=True, text=True, timeout=10)
            
            output = result.stdout + result.stderr
            
            # Se há erro de sintaxe, vai aparecer na saída
            self.assertNotIn("SyntaxError", output, f"Erro de sintaxe encontrado: {output}")
            self.assertNotIn("IndentationError", output, f"Erro de indentação encontrado: {output}")
            
            # Se executou mas falhou na conexão, isso é esperado
            if "ERRO_EXECUCAO" in output:
                # Verifica se é um erro de conexão (esperado) e não de sintaxe
                self.assertTrue(
                    any(keyword in output.lower() for keyword in [
                        "connection", "conexão", "connect", "network", "timeout"
                    ]),
                    f"Erro inesperado (não de conexão): {output}"
                )
            
        except subprocess.TimeoutExpired:
            # Se o timeout expirar, pode estar tentando conectar - isso é OK
            pass
        except Exception as e:
            self.fail(f"Erro inesperado ao executar o script: {e}")
    
    def test_conteudo_basico(self):
        """Testa se o arquivo contém o conteúdo básico esperado"""
        # Usa subprocess para ler o arquivo, já que o Python direto não funciona
        try:
            result = subprocess.run([
                sys.executable, '-c', 
                '''
import os
if os.path.exists("Teste.py"):
    with open("Teste.py", "r", encoding="utf-8") as f:
        content = f.read()
    if content.strip():
        print("CONTEUDO_ENCONTRADO")
        if "openai" in content.lower():
            print("OPENAI_ENCONTRADO")
        if "history" in content:
            print("HISTORY_ENCONTRADO")
        if "try:" in content:
            print("TRY_ENCONTRADO")
    else:
        print("ARQUIVO_VAZIO")
else:
    print("ARQUIVO_NAO_EXISTE")
                '''
            ], capture_output=True, text=True, timeout=5)
            
            output = result.stdout.strip()
            
            if "ARQUIVO_NAO_EXISTE" in output:
                self.fail("Arquivo Teste.py não existe")
            elif "ARQUIVO_VAZIO" in output:
                self.skipTest("Arquivo Teste.py está vazio")
            elif "CONTEUDO_ENCONTRADO" in output:
                self.assertIn("OPENAI_ENCONTRADO", output, "Deve importar OpenAI")
                self.assertIn("HISTORY_ENCONTRADO", output, "Deve ter variável history")
                self.assertIn("TRY_ENCONTRADO", output, "Deve ter tratamento de erro")
            else:
                self.fail(f"Resultado inesperado: {output}")
                
        except Exception as e:
            self.fail(f"Erro ao verificar conteúdo: {e}")
    
    def test_mock_execution(self):
        """Testa execução com mock da biblioteca OpenAI"""
        # Cria um script de teste que usa mocks
        test_script = '''
import sys
from unittest.mock import patch, Mock

# Mock da biblioteca OpenAI
with patch('openai.OpenAI') as mock_openai:
    mock_client = Mock()
    mock_openai.return_value = mock_client
    
    # Mock da resposta
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = "Resposta de teste"
    mock_client.chat.completions.create.return_value = mock_response
    
    # Executa o código original
    try:
        exec(open("Teste.py").read())
        print("EXECUCAO_SUCESSO")
    except Exception as e:
        print(f"ERRO_MOCK: {e}")
'''
        
        try:
            result = subprocess.run([
                sys.executable, '-c', test_script
            ], capture_output=True, text=True, timeout=10)
            
            output = result.stdout + result.stderr
            
            if "EXECUCAO_SUCESSO" in output:
                self.assertTrue(True, "Execução com mock bem-sucedida")
            elif "ERRO_MOCK" in output:
                # Se há erro no mock, pode ser problema com o arquivo
                self.assertNotIn("SyntaxError", output, f"Erro de sintaxe: {output}")
                self.assertNotIn("IndentationError", output, f"Erro de indentação: {output}")
            else:
                # Se não há saída clara, verifica se não há erros graves
                self.assertNotIn("SyntaxError", output, f"Erro de sintaxe: {output}")
                
        except subprocess.TimeoutExpired:
            # Timeout pode indicar que está funcionando mas tentando conectar
            pass
        except Exception as e:
            self.fail(f"Erro no teste de mock: {e}")

if __name__ == '__main__':
    print("=== Executando testes simples para Teste.py ===")
    unittest.main(verbosity=2)
