import unittest
from unittest.mock import patch, Mock
import sys
import os
import subprocess

# Adiciona o diretório atual ao path para importar o módulo
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestTeste(unittest.TestCase):
    """Testes para o arquivo Teste.py"""

    def _read_file_content(self):
        """Método auxiliar para ler o conteúdo do arquivo"""
        try:
            # Tenta diferentes encodings
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    with open('Teste.py', 'r', encoding=encoding) as f:
                        content = f.read()
                    if content.strip():  # Se tem conteúdo não vazio
                        return content
                except UnicodeDecodeError:
                    continue
            return ""
        except FileNotFoundError:
            return ""

    def test_syntax_and_imports(self):
        """Testa se o arquivo tem sintaxe válida e imports corretos"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        try:
            compile(content, 'Teste.py', 'exec')
            self.assertTrue(True, "Arquivo tem sintaxe válida")
        except SyntaxError as e:
            self.fail(f"Erro de sintaxe no arquivo: {e}")

    def test_history_structure(self):
        """Testa se o histórico está estruturado corretamente"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        # Verifica se contém a definição do histórico
        self.assertIn('history = [', content)
        self.assertIn('"role": "system"', content)
        self.assertIn('"role": "user"', content)
        self.assertIn('assistente de IA', content)
        self.assertIn('fato interessante', content)

    def test_openai_configuration(self):
        """Testa se a configuração do OpenAI está correta"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        # Verifica se contém as configurações corretas
        self.assertIn('from openai import OpenAI', content)
        self.assertIn('base_url="http://127.0.0.1:1234/v1"', content)
        self.assertIn('api_key="lm-studio"', content)
        self.assertIn('model="bio-medical-llama-3-8b-i1"', content)
        self.assertIn('temperature=0.7', content)
        self.assertIn('stream=False', content)

    def test_error_handling_structure(self):
        """Testa se o tratamento de erro está implementado"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        # Verifica se contém try/except
        self.assertIn('try:', content)
        self.assertIn('except Exception as e:', content)
        self.assertIn('Não foi possível conectar ao servidor do LM Studio', content)
        self.assertIn('print(f"Erro: {e}")', content)

    def test_script_execution_mock(self):
        """Testa a execução do script com mocks"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        # Mock da classe OpenAI
        mock_openai = Mock()
        mock_client = Mock()
        mock_openai.return_value = mock_client

        # Mock da resposta
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Resposta simulada do teste"
        mock_client.chat.completions.create.return_value = mock_response

        # Executa o código com mocks
        with patch('openai.OpenAI', mock_openai):
            with patch('builtins.print') as mock_print:
                # Cria um namespace para execução
                namespace = {}
                exec(content, namespace)

                # Verifica se o OpenAI foi chamado corretamente
                mock_openai.assert_called_once_with(
                    base_url="http://127.0.0.1:1234/v1",
                    api_key="lm-studio"
                )

                # Verifica se a requisição foi feita
                mock_client.chat.completions.create.assert_called_once()

                # Verifica se algo foi impresso
                mock_print.assert_called()

    def test_error_handling_execution(self):
        """Testa se o tratamento de erro funciona corretamente"""
        content = self._read_file_content()

        if not content:
            self.skipTest("Arquivo Teste.py não encontrado ou vazio")

        # Mock da classe OpenAI que lança exceção
        mock_openai = Mock()
        mock_client = Mock()
        mock_openai.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("Erro de teste")

        # Executa o código com mocks
        with patch('openai.OpenAI', mock_openai):
            with patch('builtins.print') as mock_print:
                # Cria um namespace para execução
                namespace = {}
                exec(content, namespace)

                # Verifica se as mensagens de erro foram impressas
                calls = [str(call) for call in mock_print.call_args_list]
                error_messages = ''.join(calls)

                self.assertIn("Não foi possível conectar ao servidor do LM Studio", error_messages)
                self.assertIn("Erro de teste", error_messages)

    def test_script_runs_standalone(self):
        """Testa se o script pode ser executado de forma independente"""
        try:
            # Executa o script como um processo separado
            result = subprocess.run([
                sys.executable, 'Teste.py'
            ], capture_output=True, text=True, timeout=10, cwd='.')

            # O script deve executar sem erros de sintaxe
            # Pode falhar na conexão, mas isso é esperado
            output = result.stdout + result.stderr

            # Se há saída, deve conter uma das mensagens esperadas
            if output:
                # Ou uma resposta do LM Studio ou uma mensagem de erro de conexão
                has_valid_output = (
                    "Não foi possível conectar" in output or
                    len(output.strip()) > 0  # Qualquer saída indica que executou
                )
                self.assertTrue(has_valid_output, f"Saída inesperada: {output}")

        except subprocess.TimeoutExpired:
            # Se o timeout expirar, pode ser que esteja tentando conectar
            # Isso é aceitável para este teste
            pass
        except FileNotFoundError:
            self.fail("Arquivo Teste.py não encontrado")
        except Exception as e:
            self.fail(f"Erro inesperado ao executar o script: {e}")


if __name__ == '__main__':
    # Executa os testes
    unittest.main(verbosity=2)
