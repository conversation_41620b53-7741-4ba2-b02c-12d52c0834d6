from openai import OpenAI

# Configura o cliente da OpenAI para apontar para o servidor local do LM Studio
client = OpenAI(base_url="http://127.0.0.1:1234/v1", api_key="lm-studio")

# Define o histórico da conversa
history = [
    {"role": "system", "content": "Você é um assistente de IA que adora contar fatos interessantes."},
    {"role": "user", "content": "Me diga um fato interessante sobre o universo."}
]

try:
    # Envia a requisição de conclusão de chat
    completion = client.chat.completions.create(
        model="bio-medical-llama-3-8b-i1",  # Não é usado pelo LM Studio, mas o campo é obrigatório
        messages=history,
        temperature=0.7,
        stream=False,
    )

    # Imprime a resposta
    response_message = completion.choices[0].message
    print(response_message.content)

except Exception as e:
    print(f"Não foi possível conectar ao servidor do LM Studio. Verifique se ele está em execução.")
    print(f"Erro: {e}")