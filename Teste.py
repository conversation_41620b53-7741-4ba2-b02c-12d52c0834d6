from openai import OpenAI
import sys

print("=== TESTE DE CONEXÃO COM LM STUDIO ===")
print("Iniciando script...")

# Configura o cliente da OpenAI para apontar para o servidor local do LM Studio
print("Configurando cliente OpenAI...")
client = OpenAI(base_url="http://127.0.0.1:1234/v1", api_key="lm-studio")

# Define o histórico da conversa
print("Definindo histórico da conversa...")
history = [
    {"role": "system", "content": "Você é um assistente de IA que adora contar fatos interessantes."},
    {"role": "user", "content": "Me diga um fato interessante sobre o universo."}
]

print("Tentando conectar ao LM Studio...")
try:
    # Envia a requisição de conclusão de chat
    completion = client.chat.completions.create(
        model="bio-medical-llama-3-8b-i1",  # Não é usado pelo LM Studio, mas o campo é obrigatório
        messages=history,
        temperature=0.7,
        stream=False,
    )

    # Imprime a resposta
    print("\n=== RESPOSTA DO LM STUDIO ===")
    response_message = completion.choices[0].message
    print(response_message.content)
    print("\n=== FIM DA RESPOSTA ===")

except Exception as e:
    print(f"\n❌ ERRO: Não foi possível conectar ao servidor do LM Studio.")
    print(f"Verifique se o LM Studio está em execução na porta 1234.")
    print(f"Detalhes do erro: {e}")
    print(f"Tipo do erro: {type(e).__name__}")

print("\nScript finalizado.")
sys.stdout.flush()  # Força a saída do buffer